<template>
  <div class="floating-menu" :class="{ 'menu-open': isMenuOpen }">
    <!-- 菜单触发按钮 -->
    <button class="menu-trigger" @click="toggleMenu" :class="{ active: isMenuOpen }">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>

    <!-- 章节菜单内容 -->
    <div class="menu-content" v-if="isMenuOpen">
      <div class="menu-header">
        <h3>📖 章节目录</h3>
        <button class="close-btn" @click="closeMenu">✕</button>
      </div>

      <div class="chapters-list">
        <RouterLink
          v-for="chapter in chapters"
          :key="chapter.id"
          :to="chapter.path"
          class="chapter-item"
          @click="closeMenu"
        >
          <div class="chapter-number">{{ chapter.id }}</div>
          <div class="chapter-info">
            <div class="chapter-title">{{ chapter.title }}</div>
            <div class="chapter-description">{{ chapter.description }}</div>
          </div>
          <div class="chapter-arrow">→</div>
        </RouterLink>
      </div>

      <div class="menu-footer">
        <RouterLink
          to="/books/well-grounded-java-developer"
          class="book-intro-link"
          @click="closeMenu"
        >
          📚 书籍介绍
        </RouterLink>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="isMenuOpen" class="menu-backdrop" @click="closeMenu"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

const isMenuOpen = ref(false)
const route = useRoute()

const chapters = [
  {
    id: 1,
    title: 'Introducing Modern Java',
    description: 'Java语言与平台的区别，新的发布模型',
    path: '/chapter1',
  },
  {
    id: 2,
    title: 'Java Modules (JPMS)',
    description: 'Java平台模块系统，模块化架构设计',
    path: '/chapter2',
  },
  {
    id: 3,
    title: 'Java 17 现代特性',
    description: '文本块、Switch表达式、Records等',
    path: '/chapter3',
  },
  {
    id: 4,
    title: 'Class Files & Bytecode',
    description: '类加载机制、字节码指令与反射',
    path: '/chapter4',
  },
  {
    id: 5,
    title: 'Java Concurrency Fundamentals',
    description: '深入并发编程：从理论基础到字节码实现',
    path: '/chapter5',
  },
  {
    id: 6,
    title: 'JDK Concurrency Libraries',
    description: '现代并发工具全景：原子操作到异步编程',
    path: '/chapter6',
  },
  {
    id: 7,
    title: 'Understanding Java Performance',
    description: '科学的性能分析方法论，GC、JIT与性能工具',
    path: '/chapter7',
  },
]

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

// 监听ESC键关闭菜单
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isMenuOpen.value) {
    closeMenu()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.floating-menu {
  position: fixed;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  z-index: 999;
}

.menu-trigger {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1001;
}

.menu-trigger:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: white;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.menu-trigger.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-trigger.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.menu-trigger.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.menu-content {
  position: fixed;
  top: 50%;
  left: 100px;
  transform: translateY(-50%);
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideInLeft 0.3s ease-out;
}

.menu-header {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
}

.menu-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chapters-list {
  padding: 1rem 0;
}

.chapter-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  text-decoration: none;
  color: #333;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.chapter-item:hover {
  background: #f8f9fa;
  border-left-color: #667eea;
}

.chapter-item.router-link-active {
  background: #e3f2fd;
  border-left-color: #1976d2;
}

.chapter-number {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.chapter-info {
  flex: 1;
}

.chapter-title {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.chapter-description {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

.chapter-arrow {
  color: #999;
  font-size: 1.2rem;
  margin-left: 0.5rem;
  transition: all 0.2s ease;
}

.chapter-item:hover .chapter-arrow {
  color: #667eea;
  transform: translateX(3px);
}

.menu-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

.book-intro-link {
  display: block;
  text-align: center;
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.book-intro-link:hover {
  background: #667eea;
  color: white;
}

.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .floating-menu {
    left: 10px;
  }

  .menu-trigger {
    width: 50px;
    height: 50px;
  }

  .menu-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 350px;
  }
}
</style>
